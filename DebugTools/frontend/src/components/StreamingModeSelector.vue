<template>
  <div class="streaming-mode-selector">
    <a-card title="流媒体方式选择" :bordered="false">
      <div class="mode-options">
        <a-radio-group v-model="selectedMode" @change="onModeChange">
          <a-space direction="vertical" size="large">
            <!-- 传统方式 -->
            <a-radio value="traditional">
              <div class="mode-option">
                <div class="mode-header">
                  <icon-image class="mode-icon" />
                  <span class="mode-title">传统截图流媒体</span>
                  <a-tag color="orange" size="small">稳定</a-tag>
                </div>
                <div class="mode-description">
                  基于ADB截图的传统方式，兼容性好，延迟较高
                </div>
                <div class="mode-specs">
                  <span>延迟: 500-1000ms</span>
                  <span>帧率: 10-20fps</span>
                  <span>格式: JPEG</span>
                </div>
              </div>
            </a-radio>

            <!-- FFmpeg方式 -->
            <a-radio value="ffmpeg" :disabled="!ffmpegAvailable">
              <div class="mode-option">
                <div class="mode-header">
                  <icon-video-camera class="mode-icon" />
                  <span class="mode-title">FFmpeg高性能流媒体</span>
                  <a-tag color="green" size="small">推荐</a-tag>
                  <a-tag v-if="!ffmpegAvailable" color="red" size="small">不可用</a-tag>
                </div>
                <div class="mode-description">
                  基于FFmpeg的H.264视频流，低延迟高质量，支持硬件加速
                </div>
                <div class="mode-specs">
                  <span>延迟: 100-300ms</span>
                  <span>帧率: 30-60fps</span>
                  <span>格式: H.264</span>
                </div>
                <div v-if="ffmpegInfo" class="ffmpeg-info">
                  <icon-check-circle style="color: green;" />
                  <span>{{ ffmpegInfo }}</span>
                </div>
              </div>
            </a-radio>
          </a-space>
        </a-radio-group>
      </div>

      <div class="mode-actions">
        <a-space>
          <a-button @click="testFFmpeg" :loading="testingFFmpeg">
            <template #icon><icon-tool /></template>
            测试FFmpeg
          </a-button>
          <a-button 
            type="primary" 
            @click="applyMode"
            :disabled="!selectedMode"
          >
            应用设置
          </a-button>
        </a-space>
      </div>

      <!-- FFmpeg安装指南 -->
      <div v-if="!ffmpegAvailable" class="ffmpeg-install-guide">
        <a-alert
          type="warning"
          title="FFmpeg未安装或不可用"
          show-icon
        >
          <template #description>
            <div class="install-steps">
              <p>请按以下步骤安装FFmpeg：</p>
              <ol>
                <li>
                  <strong>macOS:</strong>
                  <a-typography-text code>brew install ffmpeg</a-typography-text>
                </li>
                <li>
                  <strong>Ubuntu/Debian:</strong>
                  <a-typography-text code>sudo apt install ffmpeg</a-typography-text>
                </li>
                <li>
                  <strong>Windows:</strong>
                  从 <a href="https://ffmpeg.org/download.html" target="_blank">FFmpeg官网</a> 下载并添加到PATH
                </li>
              </ol>
              <p>安装完成后点击"测试FFmpeg"按钮验证。</p>
            </div>
          </template>
        </a-alert>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'

const emit = defineEmits(['mode-change'])

// 状态
const selectedMode = ref('traditional')
const ffmpegAvailable = ref(false)
const ffmpegInfo = ref('')
const testingFFmpeg = ref(false)

// FFmpeg API
const ffmpegApi = {
  async test() {
    const response = await fetch('/api/ffmpeg-streaming/test')
    return await response.json()
  }
}

// 测试FFmpeg
const testFFmpeg = async () => {
  testingFFmpeg.value = true
  try {
    const response = await ffmpegApi.test()
    
    if (response.success) {
      ffmpegAvailable.value = true
      ffmpegInfo.value = response.version
      Message.success('FFmpeg可用: ' + response.version)
      
      // 如果FFmpeg可用且当前选择的是传统方式，建议切换
      if (selectedMode.value === 'traditional') {
        Message.info('建议使用FFmpeg方式获得更好的性能')
      }
    } else {
      ffmpegAvailable.value = false
      ffmpegInfo.value = ''
      Message.error('FFmpeg不可用: ' + response.message)
      
      // 如果当前选择FFmpeg但不可用，切换到传统方式
      if (selectedMode.value === 'ffmpeg') {
        selectedMode.value = 'traditional'
        Message.warning('已切换到传统方式')
      }
    }
  } catch (error) {
    ffmpegAvailable.value = false
    ffmpegInfo.value = ''
    Message.error('测试FFmpeg失败: ' + error.message)
  } finally {
    testingFFmpeg.value = false
  }
}

// 模式变化处理
const onModeChange = (value) => {
  console.log('流媒体模式变更:', value)
  
  if (value === 'ffmpeg' && !ffmpegAvailable.value) {
    Message.warning('FFmpeg不可用，请先安装并测试')
    return
  }
  
  emit('mode-change', value)
}

// 应用设置
const applyMode = () => {
  if (!selectedMode.value) {
    Message.warning('请选择一种流媒体方式')
    return
  }
  
  if (selectedMode.value === 'ffmpeg' && !ffmpegAvailable.value) {
    Message.error('FFmpeg不可用，无法应用此设置')
    return
  }
  
  // 保存到本地存储
  localStorage.setItem('streaming_mode', selectedMode.value)
  
  emit('mode-change', selectedMode.value)
  Message.success(`已应用${selectedMode.value === 'ffmpeg' ? 'FFmpeg' : '传统'}流媒体方式`)
}

// 组件挂载时初始化
onMounted(async () => {
  // 从本地存储读取设置
  const savedMode = localStorage.getItem('streaming_mode')
  if (savedMode) {
    selectedMode.value = savedMode
  }
  
  // 自动测试FFmpeg
  await testFFmpeg()
  
  // 如果FFmpeg可用且没有保存的设置，推荐使用FFmpeg
  if (ffmpegAvailable.value && !savedMode) {
    selectedMode.value = 'ffmpeg'
    Message.info('检测到FFmpeg可用，已自动选择高性能模式')
  }
  
  // 发送初始模式
  emit('mode-change', selectedMode.value)
})
</script>

<style scoped>
.streaming-mode-selector {
  max-width: 600px;
  margin: 0 auto;
}

.mode-options {
  margin-bottom: 20px;
}

.mode-option {
  padding: 12px 0;
  border-left: 3px solid transparent;
  padding-left: 12px;
  transition: all 0.3s ease;
}

.mode-option:hover {
  border-left-color: var(--color-primary);
  background: var(--color-fill-1);
  border-radius: 4px;
}

.mode-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.mode-icon {
  font-size: 18px;
  color: var(--color-primary);
}

.mode-title {
  font-weight: 500;
  font-size: 14px;
}

.mode-description {
  color: var(--color-text-2);
  font-size: 12px;
  margin-bottom: 8px;
  line-height: 1.4;
}

.mode-specs {
  display: flex;
  gap: 12px;
  font-size: 11px;
  color: var(--color-text-3);
}

.mode-specs span {
  padding: 2px 6px;
  background: var(--color-fill-2);
  border-radius: 3px;
}

.ffmpeg-info {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 8px;
  font-size: 11px;
  color: var(--color-success);
}

.mode-actions {
  padding-top: 16px;
  border-top: 1px solid var(--color-border);
  text-align: center;
}

.ffmpeg-install-guide {
  margin-top: 20px;
}

.install-steps {
  font-size: 13px;
}

.install-steps ol {
  margin: 8px 0;
  padding-left: 20px;
}

.install-steps li {
  margin: 4px 0;
}

/* 选中状态样式 */
.arco-radio-checked + .mode-option {
  border-left-color: var(--color-primary);
  background: var(--color-primary-light-1);
}
</style>
