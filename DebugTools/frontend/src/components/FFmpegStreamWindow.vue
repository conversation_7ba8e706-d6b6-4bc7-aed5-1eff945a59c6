<template>
  <div
    v-if="visible"
    class="ffmpeg-stream-window"
    :style="windowStyle"
    @mousedown="startDrag"
  >
    <!-- 窗口标题栏 -->
    <div class="window-header" @mousedown="startDrag">
      <div class="window-title">
        <icon-video-camera />
        <span>FFmpeg高性能流媒体</span>
      </div>
      <div class="window-controls">
        <a-button size="mini" @click="toggleMinimize">
          <template #icon>
            <icon-minus v-if="!minimized" />
            <icon-plus v-else />
          </template>
        </a-button>
        <a-button size="mini" @click="toggleFullscreen">
          <template #icon>
            <icon-fullscreen v-if="!fullscreen" />
            <icon-fullscreen-exit v-else />
          </template>
        </a-button>
        <a-button size="mini" status="danger" @click="closeWindow">
          <template #icon><icon-close /></template>
        </a-button>
      </div>
    </div>

    <!-- 窗口内容 -->
    <div v-if="!minimized" class="window-content">
      <!-- HLS视频播放器 -->
      <div class="video-display">
        <video
          ref="videoPlayer"
          class="video-player"
          autoplay
          muted
          playsinline
          controls
          @click="onVideoClick"
          @loadstart="onVideoLoadStart"
          @loadeddata="onVideoLoaded"
          @error="onVideoError"
        ></video>
        
        <div v-if="!hasVideo" class="no-video">
          <a-spin size="large" />
          <p>正在连接FFmpeg流媒体...</p>
        </div>
      </div>

      <!-- 控制面板 -->
      <div class="stream-controls">
        <div class="control-row">
          <a-button
            v-if="!isStreaming"
            type="primary"
            @click="startStreaming"
            :loading="startingStream"
            size="mini"
          >
            <template #icon><icon-play-arrow /></template>
            开始
          </a-button>
          <a-button
            v-else
            status="danger"
            @click="stopStreaming"
            :loading="stoppingStream"
            size="mini"
          >
            <template #icon><icon-pause /></template>
            停止
          </a-button>

          <a-button
            v-if="!isRecording"
            @click="startRecording"
            :disabled="!isStreaming"
            size="mini"
          >
            <template #icon><icon-record /></template>
            录制
          </a-button>
          <a-button
            v-else
            status="warning"
            @click="stopRecording"
            :loading="stoppingRecord"
            size="mini"
          >
            <template #icon><icon-record-stop /></template>
            停止录制
          </a-button>

          <a-button
            @click="testFFmpeg"
            size="mini"
          >
            <template #icon><icon-tool /></template>
            测试FFmpeg
          </a-button>
        </div>

        <div class="status-row">
          <a-tag :color="connectionStatus === 'connected' ? 'green' : 'red'" size="small">
            {{ connectionStatusText }}
          </a-tag>
          <span v-if="streamUrls" class="stream-info">HLS流可用</span>
        </div>

        <!-- 设置面板 -->
        <div class="settings-row" v-if="!minimized">
          <a-form layout="inline" size="mini">
            <a-form-item label="帧率">
              <a-select
                v-model="streamSettings.fps"
                size="mini"
                style="width: 80px"
                :disabled="isStreaming"
                @change="onSettingsChange"
              >
                <a-option :value="15">15</a-option>
                <a-option :value="20">20</a-option>
                <a-option :value="30">30</a-option>
                <a-option :value="60">60</a-option>
              </a-select>
            </a-form-item>
            <a-form-item label="比特率">
              <a-select
                v-model="streamSettings.bitrate"
                size="mini"
                style="width: 80px"
                :disabled="isStreaming"
                @change="onSettingsChange"
              >
                <a-option value="1M">1M</a-option>
                <a-option value="2M">2M</a-option>
                <a-option value="4M">4M</a-option>
                <a-option value="8M">8M</a-option>
              </a-select>
            </a-form-item>
            <a-form-item label="分辨率">
              <a-select
                v-model="streamSettings.resolution"
                size="mini"
                style="width: 80px"
                :disabled="isStreaming"
                @change="onSettingsChange"
              >
                <a-option value="480p">480p</a-option>
                <a-option value="720p">720p</a-option>
                <a-option value="1080p">1080p</a-option>
              </a-select>
            </a-form-item>
          </a-form>
        </div>
      </div>
    </div>

    <!-- 调整大小手柄 -->
    <div
      v-if="!minimized && !fullscreen"
      class="resize-handle"
      @mousedown="startResize"
    ></div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted, onUnmounted } from 'vue'
import { useDeviceStore } from '@/stores/device'
import { Message } from '@arco-design/web-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close', 'coordinate-click'])

const deviceStore = useDeviceStore()

// 窗口状态
const position = reactive({ x: 150, y: 150 })
const size = reactive({ width: 640, height: 480 })
const minimized = ref(false)
const fullscreen = ref(false)
const isDragging = ref(false)
const isResizing = ref(false)

// 流媒体状态
const isStreaming = ref(false)
const isRecording = ref(false)
const hasVideo = ref(false)
const connectionStatus = ref('disconnected')
const streamUrls = ref(null)

const startingStream = ref(false)
const stoppingStream = ref(false)
const stoppingRecord = ref(false)

// 流媒体设置
const streamSettings = reactive({
  fps: 30,
  bitrate: "2M",
  resolution: "720p"
})

// DOM引用
const videoPlayer = ref(null)

// 计算属性
const windowStyle = computed(() => {
  if (fullscreen.value) {
    return {
      position: 'fixed',
      top: '0',
      left: '0',
      width: '100vw',
      height: '100vh',
      zIndex: 9999
    }
  }
  
  return {
    position: 'fixed',
    left: `${position.x}px`,
    top: `${position.y}px`,
    width: `${size.width}px`,
    height: minimized.value ? 'auto' : `${size.height}px`,
    zIndex: 9999
  }
})

const connectionStatusText = computed(() => {
  switch (connectionStatus.value) {
    case 'connected': return '已连接'
    case 'connecting': return '连接中'
    case 'disconnected': return '未连接'
    default: return '未知状态'
  }
})

// FFmpeg API调用
const ffmpegApi = {
  async startStreaming(options) {
    const response = await fetch('/api/ffmpeg-streaming/start', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(options)
    })
    return await response.json()
  },

  async stopStreaming() {
    const response = await fetch('/api/ffmpeg-streaming/stop', {
      method: 'POST'
    })
    return await response.json()
  },

  async startRecording(options) {
    const response = await fetch('/api/ffmpeg-streaming/recording/start', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(options)
    })
    return await response.json()
  },

  async stopRecording() {
    const response = await fetch('/api/ffmpeg-streaming/recording/stop', {
      method: 'POST'
    })
    return await response.json()
  },

  async getStatus() {
    const response = await fetch('/api/ffmpeg-streaming/status')
    return await response.json()
  },

  async testFFmpeg() {
    const response = await fetch('/api/ffmpeg-streaming/test')
    return await response.json()
  }
}

// 流媒体功能
const startStreaming = async () => {
  if (!deviceStore.isConnected) {
    Message.error('请先连接设备')
    return
  }

  startingStream.value = true
  try {
    const response = await ffmpegApi.startStreaming({
      device_id: deviceStore.currentDeviceId,
      fps: streamSettings.fps,
      bitrate: streamSettings.bitrate,
      resolution: streamSettings.resolution
    })

    if (response.success) {
      isStreaming.value = true
      connectionStatus.value = 'connecting'
      streamUrls.value = response.status.stream_urls

      // 启动HLS视频播放
      if (streamUrls.value?.hls_url) {
        startHLSPlayback(streamUrls.value.hls_url)
      }

      Message.success('FFmpeg流媒体已开始')
    } else {
      throw new Error(response.message || '启动失败')
    }

  } catch (error) {
    console.error('启动FFmpeg流媒体失败:', error)
    Message.error('启动FFmpeg流媒体失败: ' + error.message)
  } finally {
    startingStream.value = false
  }
}

const stopStreaming = async () => {
  stoppingStream.value = true
  try {
    await ffmpegApi.stopStreaming()
    
    isStreaming.value = false
    hasVideo.value = false
    connectionStatus.value = 'disconnected'
    streamUrls.value = null
    
    // 停止视频播放
    if (videoPlayer.value) {
      videoPlayer.value.src = ''
    }
    
    Message.success('FFmpeg流媒体已停止')
    
  } catch (error) {
    Message.error('停止FFmpeg流媒体失败: ' + error.message)
  } finally {
    stoppingStream.value = false
  }
}

const startHLSPlayback = (hlsUrl) => {
  if (!videoPlayer.value) return

  // 设置视频源
  videoPlayer.value.src = hlsUrl
  
  // 尝试播放
  videoPlayer.value.play().catch(error => {
    console.warn('自动播放失败:', error)
  })
}

const startRecording = async () => {
  try {
    const response = await ffmpegApi.startRecording({
      device_id: deviceStore.currentDeviceId
    })

    if (response.success) {
      isRecording.value = true
      Message.success('FFmpeg录制已开始')
    } else {
      throw new Error(response.message || '录制失败')
    }
  } catch (error) {
    Message.error('开始FFmpeg录制失败: ' + error.message)
  }
}

const stopRecording = async () => {
  stoppingRecord.value = true
  try {
    const response = await ffmpegApi.stopRecording()

    if (response.success) {
      Message.success('FFmpeg录制已完成')
    } else {
      throw new Error(response.message || '停止录制失败')
    }

    isRecording.value = false

  } catch (error) {
    Message.error('停止FFmpeg录制失败: ' + error.message)
  } finally {
    stoppingRecord.value = false
  }
}

const testFFmpeg = async () => {
  try {
    const response = await ffmpegApi.testFFmpeg()

    if (response.success) {
      Message.success(`FFmpeg可用: ${response.version}`)
    } else {
      Message.error(`FFmpeg不可用: ${response.message}`)
    }
  } catch (error) {
    Message.error('测试FFmpeg失败: ' + error.message)
  }
}

// 视频事件处理
const onVideoClick = (event) => {
  if (!hasVideo.value) return

  const video = videoPlayer.value
  const rect = video.getBoundingClientRect()

  // 计算在视频上的相对坐标
  const videoX = event.clientX - rect.left
  const videoY = event.clientY - rect.top

  // 计算相对位置（0-1之间）
  const relativeX = videoX / rect.width
  const relativeY = videoY / rect.height

  // 转换为设备上的实际坐标（需要根据设备分辨率调整）
  const deviceWidth = 1080  // 临时值，应该从设备信息获取
  const deviceHeight = 1920 // 临时值，应该从设备信息获取

  const x = Math.round(relativeX * deviceWidth)
  const y = Math.round(relativeY * deviceHeight)

  emit('coordinate-click', { x, y })
  Message.info(`点击坐标: (${x}, ${y})`)
}

const onVideoLoadStart = () => {
  console.log('视频开始加载')
  connectionStatus.value = 'connecting'
}

const onVideoLoaded = () => {
  console.log('视频加载完成')
  hasVideo.value = true
  connectionStatus.value = 'connected'
}

const onVideoError = (error) => {
  console.error('视频播放错误:', error)
  hasVideo.value = false
  connectionStatus.value = 'disconnected'
  Message.error('视频播放失败')
}

// 窗口控制功能
const startDrag = (event) => {
  if (fullscreen.value) return

  isDragging.value = true
  const startX = event.clientX - position.x
  const startY = event.clientY - position.y

  const onMouseMove = (e) => {
    if (!isDragging.value) return
    position.x = Math.max(0, Math.min(window.innerWidth - size.width, e.clientX - startX))
    position.y = Math.max(0, Math.min(window.innerHeight - 100, e.clientY - startY))
  }

  const onMouseUp = () => {
    isDragging.value = false
    document.removeEventListener('mousemove', onMouseMove)
    document.removeEventListener('mouseup', onMouseUp)
  }

  document.addEventListener('mousemove', onMouseMove)
  document.addEventListener('mouseup', onMouseUp)
  event.preventDefault()
}

const startResize = (event) => {
  isResizing.value = true
  const startX = event.clientX
  const startY = event.clientY
  const startWidth = size.width
  const startHeight = size.height

  const onMouseMove = (e) => {
    if (!isResizing.value) return
    const deltaX = e.clientX - startX
    const deltaY = e.clientY - startY

    size.width = Math.max(400, startWidth + deltaX)
    size.height = Math.max(300, startHeight + deltaY)
  }

  const onMouseUp = () => {
    isResizing.value = false
    document.removeEventListener('mousemove', onMouseMove)
    document.removeEventListener('mouseup', onMouseUp)
  }

  document.addEventListener('mousemove', onMouseMove)
  document.addEventListener('mouseup', onMouseUp)
  event.preventDefault()
  event.stopPropagation()
}

const toggleMinimize = () => {
  minimized.value = !minimized.value
}

const toggleFullscreen = () => {
  fullscreen.value = !fullscreen.value
}

const closeWindow = () => {
  if (isStreaming.value) {
    stopStreaming()
  }
  emit('close')
}

const onSettingsChange = () => {
  if (isStreaming.value) {
    Message.info('设置将在下次启动流媒体时生效')
  }
}

// 监听可见性变化
watch(() => props.visible, async (visible) => {
  if (visible) {
    // 窗口显示时测试FFmpeg
    console.log('FFmpeg窗口显示')
  } else {
    // 窗口隐藏时停止流媒体
    if (isStreaming.value) {
      console.log('FFmpeg窗口隐藏，停止流媒体')
      stopStreaming()
    }
  }
})

// 生命周期
onMounted(() => {
  console.log('FFmpeg流媒体组件已挂载')
})

onUnmounted(() => {
  if (isStreaming.value) {
    stopStreaming()
  }
})
</script>

<style scoped>
.ffmpeg-stream-window {
  background: var(--color-bg-2);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  user-select: none;
  min-width: 400px;
  min-height: 300px;
}

.window-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: var(--color-fill-2);
  border-bottom: 1px solid var(--color-border);
  cursor: move;
}

.window-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
  color: var(--color-text-1);
}

.window-controls {
  display: flex;
  gap: 4px;
}

.window-content {
  display: flex;
  flex-direction: column;
  height: calc(100% - 41px);
}

.video-display {
  flex: 1;
  position: relative;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.video-player {
  width: 100%;
  height: 100%;
  cursor: crosshair;
  object-fit: contain;
}

.no-video {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #fff;
}

.no-video p {
  margin-top: 12px;
  font-size: 12px;
}

.stream-controls {
  padding: 8px;
  background: var(--color-fill-1);
  border-top: 1px solid var(--color-border);
}

.control-row {
  display: flex;
  gap: 4px;
  margin-bottom: 6px;
  flex-wrap: wrap;
}

.status-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
}

.stream-info {
  color: var(--color-text-3);
  font-family: monospace;
}

.settings-row {
  margin-top: 6px;
  padding-top: 6px;
  border-top: 1px solid var(--color-border-2);
}

.settings-row .arco-form {
  margin: 0;
}

.settings-row .arco-form-item {
  margin-bottom: 0;
}

.settings-row .arco-form-item-label {
  font-size: 11px;
  color: var(--color-text-3);
}

.resize-handle {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 12px;
  height: 12px;
  cursor: se-resize;
  background: linear-gradient(
    -45deg,
    transparent 30%,
    var(--color-border) 30%,
    var(--color-border) 40%,
    transparent 40%,
    transparent 60%,
    var(--color-border) 60%,
    var(--color-border) 70%,
    transparent 70%
  );
}

.resize-handle:hover {
  background: linear-gradient(
    -45deg,
    transparent 30%,
    var(--color-primary) 30%,
    var(--color-primary) 40%,
    transparent 40%,
    transparent 60%,
    var(--color-primary) 60%,
    var(--color-primary) 70%,
    transparent 70%
  );
}
</style>
