<template>
  <div
    v-if="visible"
    class="floating-stream-window"
    :style="windowStyle"
    @mousedown="startDrag"
  >
    <!-- 窗口标题栏 -->
    <div class="window-header" @mousedown="startDrag">
      <div class="window-title">
        <icon-mobile />
        <span>实时设备屏幕</span>
      </div>
      <div class="window-controls">
        <a-button size="mini" @click="toggleMinimize">
          <template #icon>
            <icon-minus v-if="!minimized" />
            <icon-plus v-else />
          </template>
        </a-button>
        <a-button size="mini" @click="toggleFullscreen">
          <template #icon>
            <icon-fullscreen v-if="!fullscreen" />
            <icon-fullscreen-exit v-else />
          </template>
        </a-button>
        <a-button size="mini" status="danger" @click="closeWindow">
          <template #icon><icon-close /></template>
        </a-button>
      </div>
    </div>

    <!-- 窗口内容 -->
    <div v-if="!minimized" class="window-content">
      <!-- 流媒体显示区域 -->
      <div class="stream-display">
        <canvas
          ref="streamCanvas"
          class="stream-canvas"
          @click="onCanvasClick"
        ></canvas>
        
        <div v-if="!hasFrame" class="no-stream">
          <a-spin size="large" />
          <p>正在连接流媒体...</p>
        </div>
      </div>

      <!-- 控制面板 -->
      <div class="stream-controls">
        <div class="control-row">
          <a-button
            v-if="!isStreaming"
            type="primary"
            @click="startStreaming"
            :loading="startingStream"
            size="mini"
          >
            <template #icon><icon-play-arrow /></template>
            开始
          </a-button>
          <a-button
            v-else
            status="danger"
            @click="stopStreaming"
            :loading="stoppingStream"
            size="mini"
          >
            <template #icon><icon-pause /></template>
            停止
          </a-button>

          <a-button
            v-if="!isRecording"
            @click="startRecording"
            :disabled="!isStreaming"
            size="mini"
          >
            <template #icon><icon-record /></template>
            录制
          </a-button>
          <a-button
            v-else
            status="warning"
            @click="stopRecording"
            :loading="stoppingRecord"
            size="mini"
          >
            <template #icon><icon-record-stop /></template>
            停止录制
          </a-button>

          <a-button
            @click="takeScreenshot"
            :disabled="!isStreaming"
            :loading="takingScreenshot"
            size="mini"
          >
            <template #icon><icon-camera /></template>
            截图
          </a-button>

          <a-button
            @click="openFileManager"
            size="mini"
          >
            <template #icon><icon-folder /></template>
            文件管理
          </a-button>
        </div>

        <div class="status-row">
          <a-tag :color="connectionStatus === 'connected' ? 'green' : 'red'" size="small">
            {{ connectionStatusText }}
          </a-tag>
          <span v-if="isStreaming" class="fps-display">{{ currentFps }} FPS</span>
        </div>
      </div>
    </div>

    <!-- 调整大小手柄 -->
    <div
      v-if="!minimized && !fullscreen"
      class="resize-handle"
      @mousedown="startResize"
    ></div>

    <!-- 文件管理模态框 -->
    <StreamingFileManager v-model:visible="showFileManager" />
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted, onUnmounted } from 'vue'
import { useDeviceStore } from '@/stores/device'
import { streamingApi, StreamingWebSocket } from '@/api/streaming'
import { deviceApi } from '@/api/device'
import { Message } from '@arco-design/web-vue'
import StreamingFileManager from './StreamingFileManager.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close', 'coordinate-click'])

const deviceStore = useDeviceStore()

// 窗口状态
const position = reactive({ x: 100, y: 100 })
const size = reactive({ width: 400, height: 600 }) // 默认尺寸
const minimized = ref(false)
const fullscreen = ref(false)
const isDragging = ref(false)
const isResizing = ref(false)
const dragStart = reactive({ x: 0, y: 0, windowX: 0, windowY: 0 })
const resizeStart = reactive({ x: 0, y: 0, width: 0, height: 0 })

// 流媒体状态
const isStreaming = ref(false)
const isRecording = ref(false)
const hasFrame = ref(false)
const connectionStatus = ref('disconnected')
const currentFps = ref(0)

const startingStream = ref(false)
const stoppingStream = ref(false)
const stoppingRecord = ref(false)
const takingScreenshot = ref(false)
const showFileManager = ref(false)

// DOM引用
const streamCanvas = ref(null)

// WebSocket
let streamingWS = null
let lastFrameTime = 0
let frameCount = 0

// 计算属性
const windowStyle = computed(() => {
  if (fullscreen.value) {
    return {
      position: 'fixed',
      top: '0',
      left: '0',
      width: '100vw',
      height: '100vh',
      zIndex: 9999
    }
  }
  
  return {
    position: 'fixed',
    left: `${position.x}px`,
    top: `${position.y}px`,
    width: `${size.width}px`,
    height: minimized.value ? 'auto' : `${size.height}px`,
    zIndex: 9999
  }
})

const connectionStatusText = computed(() => {
  switch (connectionStatus.value) {
    case 'connected': return '已连接'
    case 'connecting': return '连接中'
    case 'disconnected': return '未连接'
    default: return '未知状态'
  }
})

// 拖拽功能
const startDrag = (event) => {
  if (fullscreen.value) return
  
  isDragging.value = true
  dragStart.x = event.clientX
  dragStart.y = event.clientY
  dragStart.windowX = position.x
  dragStart.windowY = position.y
  
  document.addEventListener('mousemove', onDrag)
  document.addEventListener('mouseup', stopDrag)
  event.preventDefault()
}

const onDrag = (event) => {
  if (!isDragging.value) return
  
  const deltaX = event.clientX - dragStart.x
  const deltaY = event.clientY - dragStart.y
  
  position.x = Math.max(0, Math.min(window.innerWidth - size.width, dragStart.windowX + deltaX))
  position.y = Math.max(0, Math.min(window.innerHeight - 100, dragStart.windowY + deltaY))
}

const stopDrag = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('mouseup', stopDrag)
}

// 调整大小功能
const startResize = (event) => {
  isResizing.value = true
  resizeStart.x = event.clientX
  resizeStart.y = event.clientY
  resizeStart.width = size.width
  resizeStart.height = size.height
  
  document.addEventListener('mousemove', onResize)
  document.addEventListener('mouseup', stopResize)
  event.preventDefault()
  event.stopPropagation()
}

const onResize = (event) => {
  if (!isResizing.value) return
  
  const deltaX = event.clientX - resizeStart.x
  const deltaY = event.clientY - resizeStart.y
  
  size.width = Math.max(300, resizeStart.width + deltaX)
  size.height = Math.max(200, resizeStart.height + deltaY)
}

const stopResize = () => {
  isResizing.value = false
  document.removeEventListener('mousemove', onResize)
  document.removeEventListener('mouseup', stopResize)
}

// 窗口控制
const toggleMinimize = () => {
  minimized.value = !minimized.value
}

const toggleFullscreen = () => {
  fullscreen.value = !fullscreen.value
}

const closeWindow = () => {
  if (isStreaming.value) {
    stopStreaming()
  }
  emit('close')
}

// 流媒体功能
const initWebSocket = () => {
  streamingWS = new StreamingWebSocket()
  
  streamingWS.onStatus((status) => {
    connectionStatus.value = status
  })
  
  streamingWS.onFrame((frameData) => {
    drawFrame(frameData)
    updateFps()
  })
  
  streamingWS.onError((error) => {
    Message.error('流媒体连接错误: ' + error.message)
  })
}

const drawFrame = (frameData) => {
  if (!streamCanvas.value) return

  console.log('绘制帧数据，数据长度:', frameData.data.length)

  const canvas = streamCanvas.value
  const ctx = canvas.getContext('2d')

  const img = new Image()
  img.onload = () => {
    console.log('图片加载成功，尺寸:', img.width, 'x', img.height)

    // 计算适合的尺寸
    const containerWidth = canvas.parentElement.clientWidth
    const containerHeight = canvas.parentElement.clientHeight - 60 // 减去控制面板高度

    const scale = Math.min(containerWidth / img.width, containerHeight / img.height)

    canvas.width = img.width * scale
    canvas.height = img.height * scale

    ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
    hasFrame.value = true

    console.log('帧绘制完成，canvas尺寸:', canvas.width, 'x', canvas.height)
  }

  img.onerror = (error) => {
    console.error('图片加载失败:', error)
  }

  const format = frameData.format || 'png'
  img.src = `data:image/${format};base64,${frameData.data}`
}

const updateFps = () => {
  const now = Date.now()
  frameCount++
  
  if (now - lastFrameTime >= 1000) {
    currentFps.value = frameCount
    frameCount = 0
    lastFrameTime = now
  }
}

const startStreaming = async () => {
  if (!deviceStore.isConnected) {
    Message.error('请先连接设备')
    return
  }

  if (isStreaming.value) {
    console.log('流媒体已在运行中')
    return
  }

  startingStream.value = true
  try {
    // 首先获取设备信息来设置正确的窗口尺寸
    await setWindowSizeFromDevice()

    // 启动后端流媒体服务
    const response = await streamingApi.startStreaming({
      device_id: deviceStore.currentDeviceId,
      fps: 15,
      quality: 80,
      scale: 1.0  // 使用原始尺寸
    })

    console.log('流媒体启动响应:', response)

    if (response.success) {
      isStreaming.value = true
      connectionStatus.value = 'connecting'

      // 启动成功后再连接WebSocket
      if (!streamingWS) {
        initWebSocket()
      }
      streamingWS.connect()

      Message.success('实时流媒体已开始')
    } else {
      throw new Error(response.message || '启动失败')
    }

  } catch (error) {
    console.error('启动流媒体失败:', error)
    Message.error('启动流媒体失败: ' + error.message)
  } finally {
    startingStream.value = false
  }
}

const setWindowSizeFromDevice = async () => {
  try {
    const response = await deviceApi.getDeviceInfo()
    console.log('设备信息响应:', response)

    if (response.data && response.data.screen_info) {
      const screenInfo = response.data.screen_info
      let deviceWidth = screenInfo.width || 1080
      let deviceHeight = screenInfo.height || 1920

      // 如果设备是横屏，交换宽高
      if (deviceWidth > deviceHeight) {
        [deviceWidth, deviceHeight] = [deviceHeight, deviceWidth]
      }

      // 计算合适的显示尺寸
      const maxWidth = Math.min(600, window.innerWidth * 0.4)
      const maxHeight = Math.min(800, window.innerHeight * 0.8)

      // 计算缩放比例，保持宽高比
      const scale = Math.min(maxWidth / deviceWidth, (maxHeight - 120) / deviceHeight)

      size.width = Math.round(deviceWidth * scale)
      size.height = Math.round(deviceHeight * scale) + 120 // 加上控制面板高度

      console.log(`设备屏幕: ${deviceWidth}x${deviceHeight}, 窗口尺寸: ${size.width}x${size.height}`)
      Message.info(`窗口已设置为设备实际尺寸: ${deviceWidth}x${deviceHeight}`)
    }
  } catch (error) {
    console.error('获取设备信息失败:', error)
    // 使用默认尺寸
    size.width = 400
    size.height = 600
  }
}

const stopStreaming = async () => {
  stoppingStream.value = true
  try {
    if (isRecording.value) {
      await stopRecording()
    }
    
    await streamingApi.stopStreaming()
    
    if (streamingWS) {
      streamingWS.disconnect()
    }
    
    isStreaming.value = false
    hasFrame.value = false
    currentFps.value = 0
    
    Message.success('流媒体已停止')
    
  } catch (error) {
    Message.error('停止流媒体失败: ' + error.message)
  } finally {
    stoppingStream.value = false
  }
}

const startRecording = async () => {
  try {
    const response = await streamingApi.startRecording({
      device_id: deviceStore.currentDeviceId
    })
    
    if (response.success) {
      isRecording.value = true
      Message.success('录制已开始')
    } else {
      throw new Error(response.message || '录制失败')
    }
  } catch (error) {
    Message.error('开始录制失败: ' + error.message)
  }
}

const stopRecording = async () => {
  stoppingRecord.value = true
  try {
    const response = await streamingApi.stopRecording()
    
    if (response.success) {
      Message.success('录制已完成')
    } else {
      throw new Error(response.message || '停止录制失败')
    }
    
    isRecording.value = false
    
  } catch (error) {
    Message.error('停止录制失败: ' + error.message)
  } finally {
    stoppingRecord.value = false
  }
}

const takeScreenshot = async () => {
  takingScreenshot.value = true
  try {
    const response = await streamingApi.takeScreenshot({
      device_id: deviceStore.currentDeviceId
    })
    
    if (response.success) {
      Message.success('截图完成')
    } else {
      throw new Error(response.message || '截图失败')
    }
    
  } catch (error) {
    Message.error('截图失败: ' + error.message)
  } finally {
    takingScreenshot.value = false
  }
}

const onCanvasClick = (event) => {
  if (!hasFrame.value) return

  const canvas = streamCanvas.value
  const rect = canvas.getBoundingClientRect()

  // 计算在canvas上的相对坐标
  const canvasX = event.clientX - rect.left
  const canvasY = event.clientY - rect.top

  // 计算在原始图片上的坐标
  const scaleX = canvas.width / rect.width
  const scaleY = canvas.height / rect.height

  const x = Math.round(canvasX * scaleX)
  const y = Math.round(canvasY * scaleY)

  emit('coordinate-click', { x, y })
  Message.info(`点击坐标: (${x}, ${y})`)
}

const openFileManager = () => {
  showFileManager.value = true
}

// 监听可见性变化
watch(() => props.visible, (visible) => {
  if (visible) {
    // 窗口显示时自动开始流媒体
    if (deviceStore.isConnected && !isStreaming.value && !startingStream.value) {
      console.log('窗口显示，自动启动流媒体')
      setTimeout(() => {
        startStreaming()
      }, 500) // 延迟启动，确保窗口完全显示
    }
  } else {
    // 窗口隐藏时停止流媒体
    if (isStreaming.value) {
      console.log('窗口隐藏，停止流媒体')
      stopStreaming()
    }
  }
})

// 生命周期
onUnmounted(() => {
  if (isStreaming.value) {
    stopStreaming()
  }
  
  if (streamingWS) {
    streamingWS.disconnect()
  }
  
  // 清理事件监听器
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('mouseup', stopDrag)
  document.removeEventListener('mousemove', onResize)
  document.removeEventListener('mouseup', stopResize)
})
</script>

<style scoped>
.floating-stream-window {
  background: var(--color-bg-2);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  user-select: none;
  min-width: 300px;
  min-height: 200px;
}

.window-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: var(--color-fill-2);
  border-bottom: 1px solid var(--color-border);
  cursor: move;
}

.window-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
  color: var(--color-text-1);
}

.window-controls {
  display: flex;
  gap: 4px;
}

.window-content {
  display: flex;
  flex-direction: column;
  height: calc(100% - 41px);
}

.stream-display {
  flex: 1;
  position: relative;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 150px;
}

.stream-canvas {
  max-width: 100%;
  max-height: 100%;
  cursor: crosshair;
}

.no-stream {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #fff;
}

.no-stream p {
  margin-top: 12px;
  font-size: 12px;
}

.stream-controls {
  padding: 8px;
  background: var(--color-fill-1);
  border-top: 1px solid var(--color-border);
}

.control-row {
  display: flex;
  gap: 4px;
  margin-bottom: 6px;
  flex-wrap: wrap;
}

.status-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
}

.fps-display {
  color: var(--color-text-3);
  font-family: monospace;
}

.resize-handle {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 12px;
  height: 12px;
  cursor: se-resize;
  background: linear-gradient(
    -45deg,
    transparent 30%,
    var(--color-border) 30%,
    var(--color-border) 40%,
    transparent 40%,
    transparent 60%,
    var(--color-border) 60%,
    var(--color-border) 70%,
    transparent 70%
  );
}

.resize-handle:hover {
  background: linear-gradient(
    -45deg,
    transparent 30%,
    var(--color-primary) 30%,
    var(--color-primary) 40%,
    transparent 40%,
    transparent 60%,
    var(--color-primary) 60%,
    var(--color-primary) 70%,
    transparent 70%
  );
}

/* 全屏模式样式 */
.floating-stream-window[style*="100vw"] .window-header {
  background: var(--color-fill-3);
}

.floating-stream-window[style*="100vw"] .stream-display {
  min-height: calc(100vh - 120px);
}

/* 最小化状态样式 */
.floating-stream-window[style*="auto"] .window-header {
  border-bottom: none;
}

/* 拖拽时的样式 */
.floating-stream-window:active {
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .floating-stream-window {
    min-width: 250px;
    min-height: 180px;
  }

  .control-row {
    justify-content: center;
  }

  .window-title span {
    display: none;
  }
}
</style>
