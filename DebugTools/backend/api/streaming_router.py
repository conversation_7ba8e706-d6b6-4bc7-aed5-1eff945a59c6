"""
屏幕流媒体API路由
"""

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, HTTPException, Depends
from fastapi.responses import FileResponse
from pydantic import BaseModel
from typing import Optional, Dict, Any
import os
import json
import asyncio
import logging

from services.screen_streaming_service import screen_streaming_service
# 简化设备验证，直接使用设备ID

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/streaming", tags=["屏幕流媒体"])

# 请求模型
class StreamingStartRequest(BaseModel):
    device_id: str
    fps: int = 10
    quality: int = 80
    scale: float = 0.5

class RecordingStartRequest(BaseModel):
    device_id: str
    filename: Optional[str] = None

class ScreenshotRequest(BaseModel):
    device_id: str
    filename: Optional[str] = None

@router.websocket("/stream")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket连接端点，用于实时屏幕流媒体"""
    # 设置更大的消息大小限制（10MB）
    await websocket.accept()
    screen_streaming_service.add_websocket_client(websocket)

    try:
        # 发送连接成功消息
        await websocket.send_text(json.dumps({
            "type": "connection",
            "status": "connected",
            "message": "流媒体WebSocket连接成功"
        }))

        last_frame_time = 0

        while True:
            # 检查是否有新的帧数据
            if screen_streaming_service.latest_frame:
                current_timestamp = screen_streaming_service.latest_frame.get('timestamp', 0)
                if current_timestamp > last_frame_time:
                    # 发送最新帧数据
                    frame_data = screen_streaming_service.latest_frame.copy()
                    await websocket.send_text(json.dumps(frame_data))
                    last_frame_time = current_timestamp
                    logger.info(f"发送帧数据到WebSocket客户端，时间戳: {last_frame_time}, 数据长度: {len(frame_data.get('data', ''))}")
            else:
                # 如果没有帧数据，记录状态
                if screen_streaming_service.is_streaming:
                    logger.debug("流媒体运行中但没有帧数据")

            # 等待一小段时间避免过度占用CPU
            await asyncio.sleep(0.1)  # 10fps最大频率

    except WebSocketDisconnect:
        logger.info("WebSocket客户端断开连接")
    except Exception as e:
        logger.error(f"WebSocket连接错误: {e}")
    finally:
        screen_streaming_service.remove_websocket_client(websocket)

@router.post("/start")
async def start_streaming(request: StreamingStartRequest):
    """开始屏幕流媒体"""
    try:
        # 设置设备并开始流媒体
        screen_streaming_service.set_device(request.device_id)
        success = screen_streaming_service.start_streaming(
            fps=request.fps,
            quality=request.quality,
            scale=request.scale
        )
        
        if success:
            return {
                "success": True,
                "message": "屏幕流媒体已开始",
                "status": screen_streaming_service.get_status()
            }
        else:
            raise HTTPException(status_code=500, detail="启动流媒体失败")
            
    except Exception as e:
        logger.error(f"启动流媒体失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/stop")
async def stop_streaming():
    """停止屏幕流媒体"""
    try:
        screen_streaming_service.stop_streaming()
        return {
            "success": True,
            "message": "屏幕流媒体已停止"
        }
    except Exception as e:
        logger.error(f"停止流媒体失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/status")
async def get_streaming_status():
    """获取流媒体状态"""
    return {
        "success": True,
        "data": screen_streaming_service.get_status()
    }

@router.post("/recording/start")
async def start_recording(request: RecordingStartRequest):
    """开始录制视频"""
    try:
        # 设置设备并开始录制
        screen_streaming_service.set_device(request.device_id)
        result = screen_streaming_service.start_recording(request.filename)
        
        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=500, detail=result["message"])
            
    except Exception as e:
        logger.error(f"开始录制失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/recording/stop")
async def stop_recording():
    """停止录制视频"""
    try:
        result = screen_streaming_service.stop_recording()
        
        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=500, detail=result["message"])
            
    except Exception as e:
        logger.error(f"停止录制失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/screenshot")
async def take_screenshot(request: ScreenshotRequest):
    """截图当前屏幕"""
    try:
        # 设置设备并截图
        screen_streaming_service.set_device(request.device_id)
        result = screen_streaming_service.take_screenshot(request.filename)
        
        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=500, detail=result["message"])
            
    except Exception as e:
        logger.error(f"截图失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/recordings")
async def get_recordings():
    """获取录制文件列表"""
    try:
        recordings_dir = screen_streaming_service.recording_dir
        if not os.path.exists(recordings_dir):
            return {"success": True, "data": {"recordings": []}}
        
        recordings = []
        for filename in os.listdir(recordings_dir):
            if filename.endswith(('.mp4', '.avi', '.mov')):
                filepath = os.path.join(recordings_dir, filename)
                stat = os.stat(filepath)
                recordings.append({
                    "filename": filename,
                    "size": stat.st_size,
                    "created_time": stat.st_ctime,
                    "modified_time": stat.st_mtime
                })
        
        # 按修改时间倒序排列
        recordings.sort(key=lambda x: x["modified_time"], reverse=True)
        
        return {
            "success": True,
            "data": {"recordings": recordings}
        }
        
    except Exception as e:
        logger.error(f"获取录制文件列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/recordings/{filename}")
async def download_recording(filename: str):
    """下载录制文件"""
    try:
        filepath = os.path.join(screen_streaming_service.recording_dir, filename)
        if not os.path.exists(filepath):
            raise HTTPException(status_code=404, detail="文件不存在")
        
        return FileResponse(
            filepath,
            media_type='video/mp4',
            filename=filename
        )
        
    except Exception as e:
        logger.error(f"下载录制文件失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/recordings/{filename}")
async def delete_recording(filename: str):
    """删除录制文件"""
    try:
        filepath = os.path.join(screen_streaming_service.recording_dir, filename)
        if not os.path.exists(filepath):
            raise HTTPException(status_code=404, detail="文件不存在")
        
        os.remove(filepath)
        return {
            "success": True,
            "message": f"文件 {filename} 已删除"
        }
        
    except Exception as e:
        logger.error(f"删除录制文件失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/screenshots")
async def get_screenshots():
    """获取流媒体截图列表"""
    try:
        screenshots_dir = screen_streaming_service.screenshots_dir
        if not os.path.exists(screenshots_dir):
            return {"success": True, "data": {"screenshots": []}}
        
        screenshots = []
        for filename in os.listdir(screenshots_dir):
            if filename.endswith(('.png', '.jpg', '.jpeg')):
                filepath = os.path.join(screenshots_dir, filename)
                stat = os.stat(filepath)
                screenshots.append({
                    "filename": filename,
                    "size": stat.st_size,
                    "created_time": stat.st_ctime,
                    "modified_time": stat.st_mtime
                })
        
        # 按修改时间倒序排列
        screenshots.sort(key=lambda x: x["modified_time"], reverse=True)
        
        return {
            "success": True,
            "data": {"screenshots": screenshots}
        }
        
    except Exception as e:
        logger.error(f"获取截图列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/screenshots/{filename}")
async def download_screenshot(filename: str):
    """下载截图文件"""
    try:
        filepath = os.path.join(screen_streaming_service.screenshots_dir, filename)
        if not os.path.exists(filepath):
            raise HTTPException(status_code=404, detail="文件不存在")
        
        return FileResponse(
            filepath,
            media_type='image/png',
            filename=filename
        )
        
    except Exception as e:
        logger.error(f"下载截图文件失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/screenshots/{filename}")
async def delete_screenshot(filename: str):
    """删除截图文件"""
    try:
        filepath = os.path.join(screen_streaming_service.screenshots_dir, filename)
        if not os.path.exists(filepath):
            raise HTTPException(status_code=404, detail="文件不存在")

        os.remove(filepath)
        return {
            "success": True,
            "message": f"截图 {filename} 已删除"
        }

    except Exception as e:
        logger.error(f"删除截图失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/recordings/open-folder")
async def open_recordings_folder():
    """打开录制文件夹"""
    try:
        recordings_dir = os.path.abspath(screen_streaming_service.recording_dir)

        # 确保目录存在
        os.makedirs(recordings_dir, exist_ok=True)

        # 根据操作系统打开文件夹
        import platform
        system = platform.system()

        if system == "Windows":
            os.startfile(recordings_dir)
        elif system == "Darwin":  # macOS
            os.system(f"open '{recordings_dir}'")
        else:  # Linux
            os.system(f"xdg-open '{recordings_dir}'")

        return {
            "success": True,
            "message": f"已打开录制文件夹: {recordings_dir}"
        }

    except Exception as e:
        logger.error(f"打开录制文件夹失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/screenshots/open-folder")
async def open_screenshots_folder():
    """打开截图文件夹"""
    try:
        screenshots_dir = os.path.abspath(screen_streaming_service.screenshots_dir)

        # 确保目录存在
        os.makedirs(screenshots_dir, exist_ok=True)

        # 根据操作系统打开文件夹
        import platform
        system = platform.system()

        if system == "Windows":
            os.startfile(screenshots_dir)
        elif system == "Darwin":  # macOS
            os.system(f"open '{screenshots_dir}'")
        else:  # Linux
            os.system(f"xdg-open '{screenshots_dir}'")

        return {
            "success": True,
            "message": f"已打开截图文件夹: {screenshots_dir}"
        }

    except Exception as e:
        logger.error(f"打开截图文件夹失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/test-screenshot/{device_id}")
async def test_screenshot_api(device_id: str):
    """测试截图API"""
    try:
        import subprocess
        import base64

        # 执行截图命令
        cmd = ['adb', '-s', device_id, 'exec-out', 'screencap', '-p']
        result = subprocess.run(cmd, capture_output=True, timeout=10)

        if result.returncode == 0 and result.stdout:
            # 转换为base64
            image_data = base64.b64encode(result.stdout).decode('utf-8')

            return {
                "success": True,
                "message": "截图测试成功",
                "data": {
                    "device_id": device_id,
                    "image_size": len(result.stdout),
                    "base64_size": len(image_data),
                    "image_data": image_data[:100] + "..." if len(image_data) > 100 else image_data
                }
            }
        else:
            return {
                "success": False,
                "message": f"截图失败，返回码: {result.returncode}",
                "error": result.stderr.decode() if result.stderr else "未知错误"
            }

    except Exception as e:
        logger.error(f"测试截图失败: {e}")
        return {
            "success": False,
            "message": f"测试截图失败: {str(e)}"
        }

@router.get("/test-websocket")
async def test_websocket_endpoint():
    """测试WebSocket端点状态"""
    try:
        return {
            "success": True,
            "message": "WebSocket端点正常",
            "endpoint": "/api/streaming/stream",
            "streaming_status": {
                "is_streaming": screen_streaming_service.is_streaming,
                "device_id": screen_streaming_service.device_id,
                "latest_frame_available": screen_streaming_service.latest_frame is not None,
                "websocket_clients": len(screen_streaming_service.websocket_clients)
            }
        }
    except Exception as e:
        logger.error(f"测试WebSocket端点失败: {e}")
        return {
            "success": False,
            "message": f"测试失败: {str(e)}"
        }
