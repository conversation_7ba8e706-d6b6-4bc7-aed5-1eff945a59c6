"""
FFmpeg流媒体API路由
提供基于FFmpeg的高性能视频流服务
"""

from fastapi import APIRouter, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.responses import FileResponse, StreamingResponse
from pydantic import BaseModel
from typing import Optional
import os
import logging
import asyncio

from services.ffmpeg_streaming_service import ffmpeg_streaming_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/ffmpeg-streaming", tags=["FFmpeg流媒体"])

# 请求模型
class FFmpegStreamingStartRequest(BaseModel):
    device_id: str
    fps: int = 30
    bitrate: str = "2M"
    resolution: str = "720p"

class FFmpegRecordingRequest(BaseModel):
    device_id: str
    filename: Optional[str] = None

# WebSocket端点
@router.websocket("/stream")
async def ffmpeg_websocket_endpoint(websocket: WebSocket):
    """FFmpeg流媒体WebSocket端点"""
    await websocket.accept()
    
    # 添加客户端
    ffmpeg_streaming_service.add_websocket_client(websocket)
    
    try:
        # 发送连接确认
        await websocket.send_json({
            "type": "connection",
            "message": "FFmpeg WebSocket连接已建立"
        })
        
        # 保持连接并处理消息
        while True:
            try:
                # 等待客户端消息
                data = await websocket.receive_text()
                logger.debug(f"收到WebSocket消息: {data}")
                
                # 发送状态更新
                status = ffmpeg_streaming_service.get_status()
                await websocket.send_json({
                    "type": "status",
                    "data": status
                })
                
            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"WebSocket消息处理错误: {e}")
                break
                
    except WebSocketDisconnect:
        logger.info("FFmpeg WebSocket客户端断开连接")
    except Exception as e:
        logger.error(f"FFmpeg WebSocket连接错误: {e}")
    finally:
        # 移除客户端
        ffmpeg_streaming_service.remove_websocket_client(websocket)

# 流媒体控制API
@router.post("/start")
async def start_ffmpeg_streaming(request: FFmpegStreamingStartRequest):
    """开始FFmpeg屏幕流媒体"""
    try:
        # 设置设备并开始流媒体
        ffmpeg_streaming_service.set_device(request.device_id)
        success = ffmpeg_streaming_service.start_streaming(
            fps=request.fps,
            bitrate=request.bitrate,
            resolution=request.resolution
        )
        
        if success:
            return {
                "success": True,
                "message": "FFmpeg屏幕流媒体已开始",
                "status": ffmpeg_streaming_service.get_status()
            }
        else:
            raise HTTPException(status_code=500, detail="启动FFmpeg流媒体失败")
            
    except Exception as e:
        logger.error(f"启动FFmpeg流媒体失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/stop")
async def stop_ffmpeg_streaming():
    """停止FFmpeg屏幕流媒体"""
    try:
        ffmpeg_streaming_service.stop_streaming()
        return {
            "success": True,
            "message": "FFmpeg屏幕流媒体已停止"
        }
    except Exception as e:
        logger.error(f"停止FFmpeg流媒体失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/status")
async def get_ffmpeg_streaming_status():
    """获取FFmpeg流媒体状态"""
    try:
        status = ffmpeg_streaming_service.get_status()
        return {
            "success": True,
            "data": status
        }
    except Exception as e:
        logger.error(f"获取FFmpeg流媒体状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 录制功能API
@router.post("/recording/start")
async def start_ffmpeg_recording(request: FFmpegRecordingRequest):
    """开始FFmpeg录制"""
    try:
        ffmpeg_streaming_service.set_device(request.device_id)
        result = ffmpeg_streaming_service.start_recording(request.filename)
        
        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=500, detail=result["message"])
            
    except Exception as e:
        logger.error(f"开始FFmpeg录制失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/recording/stop")
async def stop_ffmpeg_recording():
    """停止FFmpeg录制"""
    try:
        result = ffmpeg_streaming_service.stop_recording()
        
        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=500, detail=result["message"])
            
    except Exception as e:
        logger.error(f"停止FFmpeg录制失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# HLS流服务
@router.get("/hls/{filename}")
async def serve_hls_file(filename: str):
    """提供HLS文件服务"""
    try:
        filepath = os.path.join(ffmpeg_streaming_service.hls_dir, filename)
        
        if not os.path.exists(filepath):
            raise HTTPException(status_code=404, detail="HLS文件不存在")
        
        # 根据文件类型设置MIME类型
        if filename.endswith('.m3u8'):
            media_type = 'application/vnd.apple.mpegurl'
        elif filename.endswith('.ts'):
            media_type = 'video/mp2t'
        else:
            media_type = 'application/octet-stream'
        
        return FileResponse(
            filepath,
            media_type=media_type,
            headers={
                "Cache-Control": "no-cache, no-store, must-revalidate",
                "Pragma": "no-cache",
                "Expires": "0"
            }
        )
        
    except Exception as e:
        logger.error(f"提供HLS文件失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 录制文件管理
@router.get("/recordings")
async def list_ffmpeg_recordings():
    """获取FFmpeg录制文件列表"""
    try:
        recordings = []
        recording_dir = ffmpeg_streaming_service.recording_dir
        
        if os.path.exists(recording_dir):
            for filename in os.listdir(recording_dir):
                if filename.endswith(('.mp4', '.avi', '.mkv')):
                    filepath = os.path.join(recording_dir, filename)
                    stat = os.stat(filepath)
                    recordings.append({
                        "filename": filename,
                        "size": stat.st_size,
                        "created_time": stat.st_ctime,
                        "modified_time": stat.st_mtime
                    })
        
        # 按修改时间倒序排列
        recordings.sort(key=lambda x: x["modified_time"], reverse=True)
        
        return {
            "success": True,
            "data": recordings
        }
        
    except Exception as e:
        logger.error(f"获取FFmpeg录制文件列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/recordings/{filename}")
async def download_ffmpeg_recording(filename: str):
    """下载FFmpeg录制文件"""
    try:
        filepath = os.path.join(ffmpeg_streaming_service.recording_dir, filename)
        if not os.path.exists(filepath):
            raise HTTPException(status_code=404, detail="文件不存在")
        
        return FileResponse(
            filepath,
            media_type='video/mp4',
            filename=filename
        )
        
    except Exception as e:
        logger.error(f"下载FFmpeg录制文件失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 测试端点
@router.get("/test")
async def test_ffmpeg():
    """测试FFmpeg是否可用"""
    try:
        import subprocess
        result = subprocess.run(['ffmpeg', '-version'], capture_output=True, timeout=5)
        
        if result.returncode == 0:
            version_info = result.stdout.decode().split('\n')[0]
            return {
                "success": True,
                "message": "FFmpeg可用",
                "version": version_info
            }
        else:
            return {
                "success": False,
                "message": "FFmpeg不可用"
            }
            
    except FileNotFoundError:
        return {
            "success": False,
            "message": "FFmpeg未安装"
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"FFmpeg测试失败: {str(e)}"
        }
