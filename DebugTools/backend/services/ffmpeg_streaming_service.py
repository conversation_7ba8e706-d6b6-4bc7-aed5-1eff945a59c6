"""
基于FFmpeg的高性能Android设备屏幕流媒体服务
支持实时H.264视频流、低延迟传输、硬件加速等功能
"""

import asyncio
import subprocess
import threading
import time
import os
import signal
import json
from typing import Optional, Dict, Any, List
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class FFmpegStreamingService:
    def __init__(self):
        self.device_id: Optional[str] = None
        self.is_streaming = False
        self.is_recording = False
        self.ffmpeg_process: Optional[subprocess.Popen] = None
        self.record_process: Optional[subprocess.Popen] = None
        self.websocket_clients: List = []
        
        # 流媒体设置
        self.fps = 30  # 更高的帧率
        self.bitrate = "2M"  # 2Mbps比特率
        self.resolution = "720p"  # 默认分辨率
        self.preset = "ultrafast"  # FFmpeg预设：ultrafast, superfast, veryfast, faster, fast
        self.tune = "zerolatency"  # 零延迟调优
        
        # 输出设置
        self.output_format = "mpegts"  # MPEG-TS格式，适合流媒体
        self.video_codec = "libx264"  # H.264编码器
        self.audio_codec = "aac"  # AAC音频编码器
        
        # 网络设置
        self.stream_port = 8554  # RTSP端口
        self.hls_port = 8555     # HLS端口
        
        # 目录设置
        self.recording_dir = "recordings"
        self.hls_dir = "hls_output"
        
        # 确保目录存在
        os.makedirs(self.recording_dir, exist_ok=True)
        os.makedirs(self.hls_dir, exist_ok=True)
    
    def set_device(self, device_id: str):
        """设置目标设备"""
        self.device_id = device_id
        logger.info(f"设置FFmpeg流媒体目标设备: {device_id}")
    
    def add_websocket_client(self, websocket):
        """添加WebSocket客户端"""
        self.websocket_clients.append(websocket)
        logger.info(f"添加WebSocket客户端，当前客户端数: {len(self.websocket_clients)}")
    
    def remove_websocket_client(self, websocket):
        """移除WebSocket客户端"""
        if websocket in self.websocket_clients:
            self.websocket_clients.remove(websocket)
        logger.info(f"移除WebSocket客户端，当前客户端数: {len(self.websocket_clients)}")
    
    async def broadcast_to_clients(self, message: Dict[str, Any]):
        """向所有WebSocket客户端广播消息"""
        if not self.websocket_clients:
            return
        
        message_str = json.dumps(message)
        disconnected_clients = []
        
        for client in self.websocket_clients:
            try:
                await client.send_text(message_str)
            except Exception as e:
                logger.warning(f"向客户端发送消息失败: {e}")
                disconnected_clients.append(client)
        
        # 移除断开连接的客户端
        for client in disconnected_clients:
            self.remove_websocket_client(client)
    
    def start_streaming(self, fps: int = 30, bitrate: str = "2M", resolution: str = "720p") -> bool:
        """开始FFmpeg屏幕流媒体"""
        if not self.device_id:
            logger.error("未设置设备ID")
            return False

        if self.is_streaming:
            logger.warning("FFmpeg流媒体已在运行")
            return True

        # 验证设备连接
        try:
            test_cmd = ['adb', '-s', self.device_id, 'shell', 'echo', 'test']
            result = subprocess.run(test_cmd, capture_output=True, timeout=5)
            if result.returncode != 0:
                logger.error(f"设备 {self.device_id} 连接测试失败: {result.stderr.decode()}")
                return False
            logger.info(f"设备 {self.device_id} 连接正常")
        except Exception as e:
            logger.error(f"设备连接验证失败: {e}")
            return False
        
        self.fps = fps
        self.bitrate = bitrate
        self.resolution = resolution
        
        try:
            # 启动FFmpeg流媒体进程
            self.is_streaming = True
            self._start_ffmpeg_stream()
            
            logger.info(f"开始FFmpeg屏幕流媒体，FPS: {fps}, 比特率: {bitrate}, 分辨率: {resolution}")
            return True
        except Exception as e:
            logger.error(f"启动FFmpeg流媒体失败: {e}")
            self.is_streaming = False
            return False
    
    def _start_ffmpeg_stream(self):
        """启动FFmpeg流媒体进程"""
        try:
            # 构建FFmpeg命令
            # 使用adb shell screenrecord作为输入，通过管道传递给FFmpeg
            adb_cmd = [
                'adb', '-s', self.device_id, 'shell',
                'screenrecord', '--output-format=h264', '--bit-rate=8000000', 
                '--time-limit=3600', '--'  # 输出到stdout
            ]
            
            ffmpeg_cmd = [
                'ffmpeg',
                '-f', 'h264',  # 输入格式
                '-i', 'pipe:0',  # 从stdin读取
                '-c:v', self.video_codec,  # 视频编码器
                '-preset', self.preset,  # 编码预设
                '-tune', self.tune,  # 调优参数
                '-b:v', self.bitrate,  # 视频比特率
                '-r', str(self.fps),  # 帧率
                '-g', str(self.fps * 2),  # GOP大小
                '-keyint_min', str(self.fps),  # 最小关键帧间隔
                '-sc_threshold', '0',  # 禁用场景切换检测
                '-fflags', 'nobuffer',  # 禁用缓冲
                '-flags', 'low_delay',  # 低延迟标志
                '-strict', 'experimental',
                # HLS输出（用于Web播放）
                '-f', 'hls',
                '-hls_time', '1',  # 每个片段1秒
                '-hls_list_size', '3',  # 保持3个片段
                '-hls_flags', 'delete_segments',  # 删除旧片段
                '-hls_allow_cache', '0',  # 禁用缓存
                f'{self.hls_dir}/stream.m3u8',
                # MPEGTS输出（用于实时流）
                '-f', 'mpegts',
                '-muxdelay', '0.1',  # 最小复用延迟
                f'udp://127.0.0.1:{self.stream_port}'
            ]
            
            # 启动adb进程
            self.adb_process = subprocess.Popen(
                adb_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # 启动FFmpeg进程，连接adb输出
            self.ffmpeg_process = subprocess.Popen(
                ffmpeg_cmd,
                stdin=self.adb_process.stdout,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # 关闭adb进程的stdout，让FFmpeg接管
            self.adb_process.stdout.close()
            
            logger.info("FFmpeg流媒体进程已启动")
            
            # 启动监控线程
            self.monitor_thread = threading.Thread(target=self._monitor_ffmpeg, daemon=True)
            self.monitor_thread.start()
            
        except Exception as e:
            logger.error(f"启动FFmpeg进程失败: {e}")
            self.is_streaming = False
            raise
    
    def _monitor_ffmpeg(self):
        """监控FFmpeg进程"""
        while self.is_streaming and self.ffmpeg_process:
            try:
                # 检查进程状态
                if self.ffmpeg_process.poll() is not None:
                    logger.warning("FFmpeg进程意外退出")
                    self.is_streaming = False
                    break
                
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"监控FFmpeg进程时出错: {e}")
                break
    
    def stop_streaming(self):
        """停止FFmpeg屏幕流媒体"""
        self.is_streaming = False
        
        # 停止FFmpeg进程
        if self.ffmpeg_process:
            try:
                self.ffmpeg_process.terminate()
                self.ffmpeg_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.ffmpeg_process.kill()
            except Exception as e:
                logger.warning(f"停止FFmpeg进程失败: {e}")
            finally:
                self.ffmpeg_process = None
        
        # 停止adb进程
        if hasattr(self, 'adb_process') and self.adb_process:
            try:
                self.adb_process.terminate()
                self.adb_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.adb_process.kill()
            except Exception as e:
                logger.warning(f"停止adb进程失败: {e}")
            finally:
                self.adb_process = None
        
        logger.info("FFmpeg屏幕流媒体已停止")
    
    def get_stream_url(self) -> Dict[str, str]:
        """获取流媒体URL"""
        return {
            "hls_url": f"http://localhost:8000/hls/stream.m3u8",
            "udp_url": f"udp://127.0.0.1:{self.stream_port}",
            "rtmp_url": f"rtmp://localhost:1935/live/stream"
        }
    
    def get_status(self) -> Dict[str, Any]:
        """获取流媒体状态"""
        return {
            "device_id": self.device_id,
            "is_streaming": self.is_streaming,
            "is_recording": self.is_recording,
            "connected_clients": len(self.websocket_clients),
            "fps": self.fps,
            "bitrate": self.bitrate,
            "resolution": self.resolution,
            "stream_urls": self.get_stream_url() if self.is_streaming else None
        }
    
    def start_recording(self, filename: Optional[str] = None) -> Dict[str, Any]:
        """开始FFmpeg录制视频"""
        if not self.device_id:
            return {"success": False, "message": "未设置设备ID"}

        if self.is_recording:
            return {"success": False, "message": "录制已在进行中"}

        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"ffmpeg_record_{timestamp}.mp4"

        filepath = os.path.join(self.recording_dir, filename)

        try:
            # 使用FFmpeg直接录制高质量视频
            adb_cmd = [
                'adb', '-s', self.device_id, 'shell',
                'screenrecord', '--output-format=h264', '--bit-rate=12000000',
                '--time-limit=3600', '--'  # 输出到stdout
            ]

            ffmpeg_cmd = [
                'ffmpeg',
                '-f', 'h264',
                '-i', 'pipe:0',
                '-c:v', 'libx264',
                '-preset', 'medium',  # 更好的质量
                '-crf', '18',  # 高质量设置
                '-movflags', '+faststart',  # 优化MP4
                '-y',  # 覆盖输出文件
                filepath
            ]

            # 启动adb进程
            self.adb_record_process = subprocess.Popen(
                adb_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )

            # 启动FFmpeg录制进程
            self.record_process = subprocess.Popen(
                ffmpeg_cmd,
                stdin=self.adb_record_process.stdout,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )

            # 关闭adb进程的stdout
            self.adb_record_process.stdout.close()

            self.is_recording = True
            self.current_recording_file = filename

            logger.info(f"开始FFmpeg录制视频: {filename}")
            return {
                "success": True,
                "message": "录制已开始",
                "filename": filename,
                "filepath": filepath
            }

        except Exception as e:
            logger.error(f"开始FFmpeg录制失败: {e}")
            return {"success": False, "message": f"录制失败: {str(e)}"}

    def stop_recording(self) -> Dict[str, Any]:
        """停止FFmpeg录制视频"""
        if not self.is_recording:
            return {"success": False, "message": "当前没有录制任务"}

        try:
            # 停止FFmpeg录制进程
            if self.record_process:
                try:
                    # 发送SIGINT信号优雅停止
                    self.record_process.send_signal(signal.SIGINT)
                    self.record_process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    logger.warning("FFmpeg录制进程未正常结束，强制终止")
                    self.record_process.terminate()
                    self.record_process.wait(timeout=5)
                except Exception as e:
                    logger.warning(f"停止FFmpeg录制进程时出错: {e}")
                    self.record_process.terminate()
                    self.record_process.wait(timeout=5)

            # 停止adb录制进程
            if hasattr(self, 'adb_record_process') and self.adb_record_process:
                try:
                    self.adb_record_process.terminate()
                    self.adb_record_process.wait(timeout=5)
                except Exception as e:
                    logger.warning(f"停止adb录制进程时出错: {e}")

            # 等待文件写入完成
            time.sleep(2)

            local_path = os.path.join(self.recording_dir, self.current_recording_file)

            if os.path.exists(local_path) and os.path.getsize(local_path) > 0:
                self.is_recording = False
                self.record_process = None

                logger.info(f"FFmpeg录制完成: {self.current_recording_file}")
                return {
                    "success": True,
                    "message": "录制完成",
                    "filename": self.current_recording_file,
                    "filepath": local_path
                }
            else:
                raise Exception("录制文件不存在或为空")

        except Exception as e:
            logger.error(f"停止FFmpeg录制失败: {e}")
            self.is_recording = False
            self.record_process = None
            return {"success": False, "message": f"停止录制失败: {str(e)}"}

    def cleanup(self):
        """清理资源"""
        self.stop_streaming()
        if self.is_recording:
            self.stop_recording()
        self.websocket_clients.clear()

# 全局实例
ffmpeg_streaming_service = FFmpegStreamingService()
