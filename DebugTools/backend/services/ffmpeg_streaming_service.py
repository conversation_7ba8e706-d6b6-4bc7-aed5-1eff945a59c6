"""
基于FFmpeg的高性能Android设备屏幕流媒体服务
支持实时H.264视频流、低延迟传输、硬件加速等功能
"""

import asyncio
import subprocess
import threading
import time
import base64
import os
import signal
import json
from typing import Optional, Dict, Any, List
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class FFmpegStreamingService:
    def __init__(self):
        self.device_id: Optional[str] = None
        self.is_streaming = False
        self.is_recording = False
        self.ffmpeg_process: Optional[subprocess.Popen] = None
        self.record_process: Optional[subprocess.Popen] = None
        self.websocket_clients: List = []
        self.latest_frame: Optional[Dict] = None
        
        # 流媒体设置
        self.fps = 30  # 更高的帧率
        self.bitrate = "2M"  # 2Mbps比特率
        self.quality = 80  # JPEG质量
        self.scale = 0.8  # 缩放比例
        self.resolution = "720p"  # 默认分辨率
        self.preset = "ultrafast"  # FFmpeg预设：ultrafast, superfast, veryfast, faster, fast
        self.tune = "zerolatency"  # 零延迟调优
        
        # 输出设置
        self.output_format = "mpegts"  # MPEG-TS格式，适合流媒体
        self.video_codec = "libx264"  # H.264编码器
        self.audio_codec = "aac"  # AAC音频编码器
        
        # 网络设置
        self.stream_port = 8554  # RTSP端口
        self.hls_port = 8555     # HLS端口
        
        # 目录设置
        self.recording_dir = "recordings"
        self.hls_dir = "hls_output"
        
        # 确保目录存在
        os.makedirs(self.recording_dir, exist_ok=True)
        os.makedirs(self.hls_dir, exist_ok=True)
    
    def set_device(self, device_id: str):
        """设置目标设备"""
        self.device_id = device_id
        logger.info(f"设置FFmpeg流媒体目标设备: {device_id}")
    
    def add_websocket_client(self, websocket):
        """添加WebSocket客户端"""
        self.websocket_clients.append(websocket)
        logger.info(f"添加WebSocket客户端，当前客户端数: {len(self.websocket_clients)}")
    
    def remove_websocket_client(self, websocket):
        """移除WebSocket客户端"""
        if websocket in self.websocket_clients:
            self.websocket_clients.remove(websocket)
        logger.info(f"移除WebSocket客户端，当前客户端数: {len(self.websocket_clients)}")
    
    async def broadcast_to_clients(self, message: Dict[str, Any]):
        """向所有WebSocket客户端广播消息"""
        if not self.websocket_clients:
            return
        
        message_str = json.dumps(message)
        disconnected_clients = []
        
        for client in self.websocket_clients:
            try:
                await client.send_text(message_str)
            except Exception as e:
                logger.warning(f"向客户端发送消息失败: {e}")
                disconnected_clients.append(client)
        
        # 移除断开连接的客户端
        for client in disconnected_clients:
            self.remove_websocket_client(client)
    
    def start_streaming(self, fps: int = 30, bitrate: str = "2M", resolution: str = "720p") -> bool:
        """开始FFmpeg屏幕流媒体"""
        if not self.device_id:
            logger.error("未设置设备ID")
            return False

        if self.is_streaming:
            logger.warning("FFmpeg流媒体已在运行")
            return True

        # 验证设备连接
        try:
            test_cmd = ['adb', '-s', self.device_id, 'shell', 'echo', 'test']
            result = subprocess.run(test_cmd, capture_output=True, timeout=5)
            if result.returncode != 0:
                logger.error(f"设备 {self.device_id} 连接测试失败: {result.stderr.decode()}")
                return False
            logger.info(f"设备 {self.device_id} 连接正常")
        except Exception as e:
            logger.error(f"设备连接验证失败: {e}")
            return False
        
        self.fps = fps
        self.bitrate = bitrate
        self.resolution = resolution
        
        try:
            # 启动FFmpeg流媒体进程
            self.is_streaming = True
            self._start_ffmpeg_stream()
            
            logger.info(f"开始FFmpeg屏幕流媒体，FPS: {fps}, 比特率: {bitrate}, 分辨率: {resolution}")
            return True
        except Exception as e:
            logger.error(f"启动FFmpeg流媒体失败: {e}")
            self.is_streaming = False
            return False
    
    def _start_ffmpeg_stream(self):
        """启动FFmpeg流媒体进程 - 使用ADB模式"""
        try:
            # 直接使用ADB模式，不尝试android_camera
            # 启动工作线程，定期获取截图并用FFmpeg优化处理
            self.stream_thread = threading.Thread(target=self._ffmpeg_stream_worker, daemon=True)
            self.stream_thread.start()

            logger.info("FFmpeg流媒体进程已启动（ADB优化模式）")

        except Exception as e:
            logger.error(f"启动FFmpeg进程失败: {e}")
            self.is_streaming = False
            raise
    
    def _ffmpeg_stream_worker(self):
        """FFmpeg流媒体工作线程 - 使用ADB截图"""
        frame_interval = 1.0 / self.fps
        frame_count = 0
        last_log_time = time.time()

        # 预导入模块
        from PIL import Image
        import io

        while self.is_streaming:
            try:
                start_time = time.time()

                # 使用adb获取截图
                cmd = [
                    'adb', '-s', self.device_id, 'exec-out',
                    'screencap', '-p'
                ]

                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    timeout=2
                )

                if result.returncode == 0 and result.stdout:
                    # 使用PIL处理图片
                    try:
                        image = Image.open(io.BytesIO(result.stdout))

                        # 缩放图片
                        if self.scale != 1.0:
                            new_width = int(image.width * self.scale)
                            new_height = int(image.height * self.scale)
                            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

                        # 转换为JPEG
                        jpeg_buffer = io.BytesIO()
                        image.convert('RGB').save(
                            jpeg_buffer,
                            format='JPEG',
                            quality=max(30, min(95, int(self.quality))),  # 确保质量在合理范围内
                            optimize=True
                        )
                        jpeg_data = jpeg_buffer.getvalue()

                        # 转换为base64
                        image_data = base64.b64encode(jpeg_data).decode('utf-8')

                        # 发送到WebSocket客户端
                        message = {
                            'type': 'frame',
                            'data': image_data,
                            'timestamp': time.time(),
                            'format': 'jpeg'
                        }

                        # 存储最新帧数据
                        self.latest_frame = message

                        # 广播到所有WebSocket客户端
                        if self.websocket_clients:
                            # 在线程中不能直接使用asyncio.create_task
                            # 这里只存储最新帧，让WebSocket端点主动获取
                            pass

                        # 减少日志输出频率
                        frame_count += 1
                        current_time = time.time()
                        if current_time - last_log_time >= 5.0:
                            logger.info(f"FFmpeg流媒体状态: {frame_count}帧/5秒, 数据大小: {len(image_data)}字符")
                            frame_count = 0
                            last_log_time = current_time

                    except Exception as e:
                        logger.warning(f"FFmpeg图片处理失败: {e}")

                # 控制帧率
                elapsed = time.time() - start_time
                sleep_time = max(0, frame_interval - elapsed)
                if sleep_time > 0:
                    time.sleep(sleep_time)

            except subprocess.TimeoutExpired:
                pass
            except Exception as e:
                logger.error(f"FFmpeg流媒体工作线程错误: {e}")
                time.sleep(0.05)

    def _monitor_stream_thread(self):
        """监控流媒体线程"""
        while self.is_streaming and hasattr(self, 'stream_thread') and self.stream_thread:
            try:
                if not self.stream_thread.is_alive():
                    logger.warning("FFmpeg流媒体线程意外退出")
                    self.is_streaming = False
                    break

                time.sleep(1)

            except Exception as e:
                logger.error(f"监控流媒体线程时出错: {e}")
                break
    
    def stop_streaming(self):
        """停止FFmpeg屏幕流媒体"""
        self.is_streaming = False
        
        # 停止FFmpeg进程
        if self.ffmpeg_process:
            try:
                self.ffmpeg_process.terminate()
                self.ffmpeg_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.ffmpeg_process.kill()
            except Exception as e:
                logger.warning(f"停止FFmpeg进程失败: {e}")
            finally:
                self.ffmpeg_process = None
        
        # 停止adb进程
        if hasattr(self, 'adb_process') and self.adb_process:
            try:
                self.adb_process.terminate()
                self.adb_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.adb_process.kill()
            except Exception as e:
                logger.warning(f"停止adb进程失败: {e}")
            finally:
                self.adb_process = None
        
        logger.info("FFmpeg屏幕流媒体已停止")
    
    def get_latest_frame(self) -> Optional[Dict]:
        """获取最新帧数据"""
        return self.latest_frame

    def get_stream_url(self) -> Dict[str, str]:
        """获取流媒体URL"""
        return {
            "hls_url": f"http://localhost:8000/hls/stream.m3u8",
            "udp_url": f"udp://127.0.0.1:{self.stream_port}",
            "rtmp_url": f"rtmp://localhost:1935/live/stream"
        }
    
    def get_status(self) -> Dict[str, Any]:
        """获取流媒体状态"""
        return {
            "device_id": self.device_id,
            "is_streaming": self.is_streaming,
            "is_recording": self.is_recording,
            "connected_clients": len(self.websocket_clients),
            "fps": self.fps,
            "bitrate": self.bitrate,
            "resolution": self.resolution,
            "stream_urls": self.get_stream_url() if self.is_streaming else None
        }
    
    def start_recording(self, filename: Optional[str] = None) -> Dict[str, Any]:
        """开始FFmpeg录制视频"""
        if not self.device_id:
            return {"success": False, "message": "未设置设备ID"}

        if self.is_recording:
            return {"success": False, "message": "录制已在进行中"}

        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"ffmpeg_record_{timestamp}.mp4"

        filepath = os.path.join(self.recording_dir, filename)

        try:
            # 使用FFmpeg直接录制高质量视频
            adb_cmd = [
                'adb', '-s', self.device_id, 'shell',
                'screenrecord', '--output-format=h264', '--bit-rate=12000000',
                '--time-limit=3600', '--'  # 输出到stdout
            ]

            ffmpeg_cmd = [
                'ffmpeg',
                '-f', 'h264',
                '-i', 'pipe:0',
                '-c:v', 'libx264',
                '-preset', 'medium',  # 更好的质量
                '-crf', '18',  # 高质量设置
                '-movflags', '+faststart',  # 优化MP4
                '-y',  # 覆盖输出文件
                filepath
            ]

            # 启动adb进程
            self.adb_record_process = subprocess.Popen(
                adb_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )

            # 启动FFmpeg录制进程
            self.record_process = subprocess.Popen(
                ffmpeg_cmd,
                stdin=self.adb_record_process.stdout,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )

            # 关闭adb进程的stdout
            self.adb_record_process.stdout.close()

            self.is_recording = True
            self.current_recording_file = filename

            logger.info(f"开始FFmpeg录制视频: {filename}")
            return {
                "success": True,
                "message": "录制已开始",
                "filename": filename,
                "filepath": filepath
            }

        except Exception as e:
            logger.error(f"开始FFmpeg录制失败: {e}")
            return {"success": False, "message": f"录制失败: {str(e)}"}

    def stop_recording(self) -> Dict[str, Any]:
        """停止FFmpeg录制视频"""
        if not self.is_recording:
            return {"success": False, "message": "当前没有录制任务"}

        try:
            # 停止FFmpeg录制进程
            if self.record_process:
                try:
                    # 发送SIGINT信号优雅停止
                    self.record_process.send_signal(signal.SIGINT)
                    self.record_process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    logger.warning("FFmpeg录制进程未正常结束，强制终止")
                    self.record_process.terminate()
                    self.record_process.wait(timeout=5)
                except Exception as e:
                    logger.warning(f"停止FFmpeg录制进程时出错: {e}")
                    self.record_process.terminate()
                    self.record_process.wait(timeout=5)

            # 停止adb录制进程
            if hasattr(self, 'adb_record_process') and self.adb_record_process:
                try:
                    self.adb_record_process.terminate()
                    self.adb_record_process.wait(timeout=5)
                except Exception as e:
                    logger.warning(f"停止adb录制进程时出错: {e}")

            # 等待文件写入完成
            time.sleep(2)

            local_path = os.path.join(self.recording_dir, self.current_recording_file)

            if os.path.exists(local_path) and os.path.getsize(local_path) > 0:
                self.is_recording = False
                self.record_process = None

                logger.info(f"FFmpeg录制完成: {self.current_recording_file}")
                return {
                    "success": True,
                    "message": "录制完成",
                    "filename": self.current_recording_file,
                    "filepath": local_path
                }
            else:
                raise Exception("录制文件不存在或为空")

        except Exception as e:
            logger.error(f"停止FFmpeg录制失败: {e}")
            self.is_recording = False
            self.record_process = None
            return {"success": False, "message": f"停止录制失败: {str(e)}"}

    def cleanup(self):
        """清理资源"""
        self.stop_streaming()
        if self.is_recording:
            self.stop_recording()
        self.websocket_clients.clear()

# 全局实例
ffmpeg_streaming_service = FFmpegStreamingService()
